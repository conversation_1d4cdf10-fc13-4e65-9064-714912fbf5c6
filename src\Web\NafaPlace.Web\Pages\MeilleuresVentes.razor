@page "/meilleures-ventes"
@using NafaPlace.Web.Models.Catalog
@using NafaPlace.Web.Services
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject IProductService ProductService
@inject ICategoryService CategoryService
@inject ICartService CartService
@inject IJSRuntime JSRuntime
@inject NavigationManager NavigationManager
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>Meilleures Ventes - NafaPlace</PageTitle>

<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="bi bi-trophy text-warning me-3"></i>
                    Meilleures Ventes
                </h1>
                <p class="page-subtitle">Découvrez les produits les plus populaires et les plus vendus</p>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="filters-card">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="categoryFilter" class="form-label">Catégorie</label>
                            <select id="categoryFilter" class="form-select" @onchange="OnCategoryChanged">
                                <option value="">Toutes les catégories</option>
                                @if (categories != null)
                                {
                                    @foreach (var category in categories)
                                    {
                                        <option value="@category.Id">@category.Name</option>
                                    }
                                }
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="periodFilter" class="form-label">Période</label>
                            <select id="periodFilter" class="form-select" @onchange="OnPeriodChanged">
                                <option value="all">Toute période</option>
                                <option value="week">Cette semaine</option>
                                <option value="month">Ce mois</option>
                                <option value="quarter">Ce trimestre</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="sortFilter" class="form-label">Trier par</label>
                            <select id="sortFilter" class="form-select" @onchange="OnSortChanged">
                                <option value="sales-desc">Ventes décroissantes</option>
                                <option value="rating-desc">Mieux notés</option>
                                <option value="price-asc">Prix croissant</option>
                                <option value="price-desc">Prix décroissant</option>
                                <option value="name">Nom A-Z</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="itemsPerPage" class="form-label">Produits par page</label>
                            <select id="itemsPerPage" class="form-select" @onchange="OnPageSizeChanged">
                                <option value="12">12 produits</option>
                                <option value="24">24 produits</option>
                                <option value="48">48 produits</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Sellers Stats -->
    @if (products != null && products.Any())
    {
        <div class="row mb-4">
            <div class="col-12">
                <div class="bestsellers-stats">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="stat-card">
                                <h3 class="stat-number">@products.Count()</h3>
                                <p class="stat-label">Produits populaires</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-card">
                                <h3 class="stat-number">@(products.Any() && products.Any(p => p.Rating > 0) ? Math.Round(products.Where(p => p.Rating > 0).Average(p => p.Rating), 1) : 0)</h3>
                                <p class="stat-label">Note moyenne</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-card">
                                <h3 class="stat-number">@(categories?.Count(c => products.Any(p => p.CategoryId == c.Id)) ?? 0)</h3>
                                <p class="stat-label">Catégories représentées</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Products Grid -->
    <div class="row">
        <div class="col-12">
            @if (isLoading)
            {
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-3">Chargement des meilleures ventes...</p>
                </div>
            }
            else if (products == null || !products.Any())
            {
                <div class="empty-state text-center py-5">
                    <i class="bi bi-trophy display-1 text-muted"></i>
                    <h3 class="mt-3">Aucune vente trouvée</h3>
                    <p class="text-muted">Aucun produit ne correspond à vos critères de recherche.</p>
                    <a href="/catalog" class="btn btn-primary">
                        <i class="bi bi-grid me-2"></i>Voir tous les produits
                    </a>
                </div>
            }
            else
            {
                <div class="row g-4">
                    @foreach (var (product, index) in products.Select((p, i) => (p, i)))
                    {
                        <div class="col-6 col-md-3">
                            <div class="card product-card h-100">
                                <!-- Ranking Badge -->
                                @if (index < 3)
                                {
                                    <span class="badge badge-ranking position-@(index + 1)">
                                        @if (index == 0) { <i class="bi bi-trophy-fill"></i> }
                                        else if (index == 1) { <i class="bi bi-award-fill"></i> }
                                        else { <i class="bi bi-star-fill"></i> }
                                        #@(index + 1)
                                    </span>
                                }
                                else
                                {
                                    <span class="badge badge-bestseller">#@(index + 1)</span>
                                }

                                <!-- Discount Badge -->
                                @if (product.DiscountPercentage > 0)
                                {
                                    <span class="badge badge-sale">-@product.DiscountPercentage%</span>
                                }

                                <a href="/catalog/products/@product.Id">
                                    <img src="@(product.Images.Any() ? ProductService.GetImageUrl(product.Images.First()) : "/images/placeholder.png")" 
                                         class="card-img-top" alt="@product.Name">
                                </a>

                                <div class="card-body d-flex flex-column">
                                    <h6 class="card-title">@product.Name</h6>
                                    <p class="card-text text-muted small">@product.Category?.Name</p>
                                    
                                    <!-- Rating -->
                                    @if (product.Rating > 0)
                                    {
                                        <div class="rating-section mb-2">
                                            <div class="stars">
                                                @for (int i = 1; i <= 5; i++)
                                                {
                                                    @if (i <= product.Rating)
                                                    {
                                                        <i class="bi bi-star-fill text-warning"></i>
                                                    }
                                                    else if (i - 0.5 <= product.Rating)
                                                    {
                                                        <i class="bi bi-star-half text-warning"></i>
                                                    }
                                                    else
                                                    {
                                                        <i class="bi bi-star text-muted"></i>
                                                    }
                                                }
                                            </div>
                                            <small class="text-muted">(@product.ReviewCount avis)</small>
                                        </div>
                                    }
                                    
                                    <div class="price-section mt-auto">
                                        @if (product.DiscountPercentage > 0 && product.OldPrice.HasValue)
                                        {
                                            <div class="price-with-discount">
                                                <span class="current-price">@product.Price.ToString("N0") GNF</span>
                                                <span class="original-price">@product.OldPrice.Value.ToString("N0") GNF</span>
                                            </div>
                                        }
                                        else
                                        {
                                            <span class="current-price">@product.Price.ToString("N0") GNF</span>
                                        }
                                    </div>

                                    <div class="card-actions mt-3">
                                        <button class="btn btn-primary btn-sm w-100" @onclick="() => AddToCart(product.Id)">
                                            <i class="bi bi-cart-plus me-1"></i>Ajouter
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>

                <!-- Pagination -->
                @if (totalPages > 1)
                {
                    <nav aria-label="Navigation des meilleures ventes" class="mt-5">
                        <ul class="pagination justify-content-center">
                            <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                                <button class="page-link" @onclick="() => ChangePage(currentPage - 1)" disabled="@(currentPage == 1)">
                                    <i class="bi bi-chevron-left"></i>
                                </button>
                            </li>
                            
                            @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                            {
                                <li class="page-item @(i == currentPage ? "active" : "")">
                                    <button class="page-link" @onclick="() => ChangePage(i)">@i</button>
                                </li>
                            }
                            
                            <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                                <button class="page-link" @onclick="() => ChangePage(currentPage + 1)" disabled="@(currentPage == totalPages)">
                                    <i class="bi bi-chevron-right"></i>
                                </button>
                            </li>
                        </ul>
                    </nav>
                }
            }
        </div>
    </div>
</div>

@code {
    private IEnumerable<ProductDto>? products;
    private IEnumerable<CategoryDto>? categories;
    private bool isLoading = true;
    private int currentPage = 1;
    private int pageSize = 12;
    private int totalPages = 1;
    private int totalItems = 0;
    private int? selectedCategoryId;
    private string selectedPeriod = "all";
    private string sortBy = "sales-desc";
    private string? _userId;

    [CascadingParameter]
    public Task<AuthenticationState> AuthenticationStateTask { get; set; } = default!;

    protected override async Task OnInitializedAsync()
    {
        // Récupérer l'utilisateur connecté
        var authState = await AuthenticationStateTask;
        var user = authState.User;
        if (user.Identity?.IsAuthenticated == true)
        {
            _userId = user.FindFirst(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
        }

        await LoadCategories();
        await LoadProducts();
    }

    private async Task LoadCategories()
    {
        try
        {
            categories = await CategoryService.GetAllCategoriesAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading categories: {ex.Message}");
        }
    }

    private async Task LoadProducts()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            // Pour l'instant, utilisons les produits en vedette comme meilleures ventes
            var allProducts = await ProductService.GetFeaturedProductsAsync(100);
            
            // Filtrer par catégorie si sélectionnée
            if (selectedCategoryId.HasValue)
            {
                allProducts = allProducts.Where(p => p.CategoryId == selectedCategoryId.Value);
            }

            // Simuler un tri par ventes (en utilisant les notes et les avis comme proxy)
            allProducts = sortBy switch
            {
                "rating-desc" => allProducts.OrderByDescending(p => p.Rating).ThenByDescending(p => p.ReviewCount),
                "price-asc" => allProducts.OrderBy(p => p.Price),
                "price-desc" => allProducts.OrderByDescending(p => p.Price),
                "name" => allProducts.OrderBy(p => p.Name),
                _ => allProducts.OrderByDescending(p => p.ReviewCount).ThenByDescending(p => p.Rating)
            };

            totalItems = allProducts.Count();
            totalPages = (int)Math.Ceiling((double)totalItems / pageSize);
            
            // Pagination
            products = allProducts
                .Skip((currentPage - 1) * pageSize)
                .Take(pageSize)
                .ToList();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading products: {ex.Message}");
            products = new List<ProductDto>();
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task OnCategoryChanged(ChangeEventArgs e)
    {
        selectedCategoryId = string.IsNullOrEmpty(e.Value?.ToString()) ? null : int.Parse(e.Value.ToString()!);
        currentPage = 1;
        await LoadProducts();
    }

    private async Task OnPeriodChanged(ChangeEventArgs e)
    {
        selectedPeriod = e.Value?.ToString() ?? "all";
        currentPage = 1;
        await LoadProducts();
    }

    private async Task OnSortChanged(ChangeEventArgs e)
    {
        sortBy = e.Value?.ToString() ?? "sales-desc";
        currentPage = 1;
        await LoadProducts();
    }

    private async Task OnPageSizeChanged(ChangeEventArgs e)
    {
        pageSize = int.Parse(e.Value?.ToString() ?? "12");
        currentPage = 1;
        await LoadProducts();
    }

    private async Task ChangePage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            await LoadProducts();
        }
    }

    private async Task AddToCart(int productId)
    {
        Console.WriteLine($"🔍 DEBUG Meilleures Ventes: Tentative d'ajout au panier - UserId: {_userId}, ProductId: {productId}");

        string userId;
        if (string.IsNullOrEmpty(_userId))
        {
            Console.WriteLine("🔍 DEBUG Meilleures Ventes: Utilisateur non connecté, utilisation d'un ID invité");
            userId = await GetOrCreateGuestUserId();
        }
        else
        {
            userId = _userId;
        }

        try
        {
            Console.WriteLine($"🛒 DEBUG Meilleures Ventes: Création de l'item panier - ProductId: {productId}, Quantity: 1");
            var cartItem = new CartItemCreateDto { ProductId = productId, Quantity = 1 };

            Console.WriteLine($"📡 DEBUG Meilleures Ventes: Appel API AddItemToCartAsync...");
            var result = await CartService.AddItemToCartAsync(userId, cartItem);

            Console.WriteLine($"✅ DEBUG Meilleures Ventes: Produit ajouté avec succès - ItemCount: {result?.ItemCount ?? 0}");

            // Notification de succès
            await JSRuntime.InvokeVoidAsync("showToast", $"✅ Produit ajouté au panier !", "success");
            Console.WriteLine($"Produit {productId} ajouté au panier.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ DEBUG Meilleures Ventes: Erreur lors de l'ajout au panier: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("showToast", $"❌ Erreur: {ex.Message}", "error");
        }
    }

    private async Task<string> GetOrCreateGuestUserId()
    {
        // Utiliser le localStorage pour stocker l'ID de session invité
        var guestId = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "guestUserId");

        if (string.IsNullOrEmpty(guestId))
        {
            guestId = $"guest_{Guid.NewGuid():N}";
            await JSRuntime.InvokeVoidAsync("localStorage.setItem", "guestUserId", guestId);
        }

        return guestId;
    }
}
