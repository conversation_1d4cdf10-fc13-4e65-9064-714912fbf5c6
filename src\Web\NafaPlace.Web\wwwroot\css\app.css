/* Variables */
:root {
    --primary-color: #f27a1a;
    --primary-hover: #e06500;
    --secondary-color: #4a4a4a;
    --accent-color: #ffc107;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --text-color: #333333;
    --text-muted: #6c757d;
    --border-color: #dee2e6;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.3s ease;
}

/* Base */
html, body {
    font-family: 'Poppins', sans-serif;
    color: var(--text-color);
    background-color: #f9f9f9;
    scroll-behavior: smooth;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-hover);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover, .btn-primary:focus, .btn-primary:active {
    background-color: var(--primary-hover) !important;
    border-color: var(--primary-hover) !important;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover, .btn-outline-primary:focus, .btn-outline-primary:active {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

/* Loading screen */
.loading-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background: linear-gradient(135deg, #003366 0%, #004080 100%);
    color: white;
}

.loading-logo {
    margin-bottom: 2rem;
    animation: logoFloat 3s ease-in-out infinite;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.loading-logo img {
    max-width: 150px;
    height: auto;
}

.loading-spinner {
    margin-bottom: 1.5rem;
}

.loading-spinner .spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.3em;
    color: #E73C30;
}

.loading-text {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    text-align: center;
    letter-spacing: 0.5px;
}

@keyframes logoFloat {
    0%, 100% {
        transform: translateY(0px) scale(1);
    }
    50% {
        transform: translateY(-10px) scale(1.02);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Navigation */
.navbar {
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.navbar-brand {
    font-weight: 700;
}

.nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: var(--transition);
}

.nav-link:hover {
    color: var(--primary-color);
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Product cards */
.product-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card .card-img-top {
    height: 200px;
    object-fit: cover;
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
}

.product-card .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-card .product-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 1rem;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product-card .product-price {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1.25rem;
    margin-top: auto;
    margin-bottom: 1rem;
}

.product-card .product-rating {
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

/* Forms */
.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(242, 122, 26, 0.25);
}

.input-group-text {
    background-color: var(--light-color);
    border-color: var(--border-color);
}

/* Footer */
.footer {
    background-color: white;
    border-top: 1px solid var(--border-color);
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
}

/* Pagination */
.pagination .page-link {
    color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .navbar-brand img {
        height: 30px;
    }
    
    .product-card .card-img-top {
        height: 150px;
    }
}

/* Error UI */
#blazor-error-ui {
    background-color: var(--danger-color);
    color: white;
    bottom: 0;
    box-shadow: 0 -2px 5px rgba(0,0,0,0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

#blazor-error-ui .dismiss {
    cursor: pointer;
    position: absolute;
    right: 0.75rem;
    top: 0.5rem;
}

/* Auth pages */
.auth-container {
    min-height: calc(100vh - 200px);
    display: flex;
    align-items: center;
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-hover);
}

/* Page */
.page {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

main {
    flex: 1;
    padding-top: 60px;
}

/* Price tags */
.price-tag {
    font-size: 1.25rem;
    font-weight: bold;
    color: var(--primary-color);
}

/* Rating stars */
.rating {
    color: var(--accent-color);
}

.rating .bi-star-fill,
.rating .bi-star {
    font-size: 0.875rem;
}

/* Category pills */
.category-pill {
    background-color: #e9ecef;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    margin: 0.25rem;
    display: inline-block;
    transition: background-color 0.2s;
}

.category-pill:hover {
    background-color: #dee2e6;
    cursor: pointer;
}

/* Product grid */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    padding: 1rem;
}

/* Media Queries */
@media (max-width: 768px) {
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
}

@media (max-width: 576px) {
    .product-grid {
        grid-template-columns: 1fr;
    }
}

/* Hero section */
.hero-section {
    margin-bottom: 3rem;
}

.hero-section .carousel-item {
    height: 500px;
}

.hero-section .carousel-item img {
    height: 100%;
    object-fit: cover;
    filter: brightness(0.7);
}

.hero-section .carousel-caption {
    bottom: 30%;
}

.hero-section h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-section p {
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Category cards */
.category-card {
    transition: var(--transition);
    cursor: pointer;
}

.category-card:hover {
    transform: translateY(-5px);
}

.category-card .bi {
    transition: var(--transition);
}

.category-card:hover .bi {
    transform: scale(1.2);
}

/* Newsletter section */
.newsletter-section {
    background-color: var(--light-color);
    padding: 4rem 0;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .hero-section .carousel-item {
        height: 300px;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .hero-section p {
        font-size: 1rem;
    }
    
    .hero-section .carousel-caption {
        bottom: 20%;
    }
}

/* Dark mode styles */
.dark-mode {
    background-color: #121212;
    color: #f8f9fa;
}

.dark-mode .card {
    background-color: #1e1e1e;
    color: #f8f9fa;
}

.dark-mode .bg-light {
    background-color: #1e1e1e !important;
}

.dark-mode .navbar,
.dark-mode .footer {
    background-color: #1e1e1e;
    color: #f8f9fa;
}
